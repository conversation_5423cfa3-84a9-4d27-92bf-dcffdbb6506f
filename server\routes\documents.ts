import { Router } from 'express';
import { z } from 'zod';
import { eventBus } from '../../lib/eventBus';
import { ActivityLogHelpers, createActivityLog, getUserEmailFromRequest } from '../lib/activityLogger';
import { batchFetchDocuments } from '../lib/batchUtils';
import { getSupabaseErrorMessage } from '../lib/httpErrors';
import { cacheKeys, deleteFromCache } from '../lib/redis';
import { AuthRequest, getAccessToken, requireAuth } from '../middleware/auth';
import { getUserClient } from '../supabaseClient';

// Import custom error types for better error handling
class ValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

class DatabaseError extends Error {
  constructor(message: string, public originalError?: Error) {
    super(message);
    this.name = 'DatabaseError';
  }
}

class CacheError extends Error {
  constructor(message: string, public originalError?: Error) {
    super(message);
    this.name = 'CacheError';
  }
}

const router = Router();

const docInsertSchema = z.object({
  name: z.string().min(1),
  content: z.string().default(''),
  folderId: z.string().uuid().optional().nullable(),
  status: z.string().default('draft'),
  clientId: z.string().uuid().optional().nullable(),
  value: z.number().optional(),
  tags: z.array(z.string()).optional(),
});

const docUpdateSchema = z.object({
  name: z.string().min(1).optional(),
  content: z.string().optional(),
  folderId: z.string().uuid().nullable().optional(),
  status: z.string().optional(),
  clientId: z.string().uuid().nullable().optional(),
  value: z.number().optional(),
  tags: z.array(z.string()).optional(),
});

const batchRequestSchema = z.object({
  documentIds: z.array(z.string().uuid()).max(50),
  includeComments: z.boolean().optional().default(false),
  includeMetadata: z.boolean().optional().default(false),
  includeActivityLogs: z.boolean().optional().default(false),
  includeVersions: z.boolean().optional().default(false),
  includeCollaborators: z.boolean().optional().default(false),
  includeSignatures: z.boolean().optional().default(false),
});

const MAX_DOCUMENTS_PAGE_SIZE = 50;

const documentsListQuerySchema = z.object({
  limit: z.coerce.number().int().min(1).max(MAX_DOCUMENTS_PAGE_SIZE).default(20),
  offset: z.coerce.number().int().min(0).default(0),
});

// List current user's documents
router.get('/', requireAuth, async (req: AuthRequest, res) => {
  const userId = req.user?.id;
  if (!userId) {return res.status(401).json({ error: 'User not authenticated' });}

  const parsedQuery = documentsListQuerySchema.safeParse(req.query);
  if (!parsedQuery.success) {
    return res.status(400).json({ error: parsedQuery.error.message });
  }

  const { limit, offset } = parsedQuery.data;

  try {
    const userClient = getUserClient(getAccessToken(req));

    // First get basic document list
    const { data: basicDocs, error, count } = await userClient
      .from('documents')
      .select('id, name, status, folder_id, created_at, updated_at, content, client_id, value', { count: 'exact' })
      .order('updated_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {return res.status(500).json({ error: getSupabaseErrorMessage(error) });}

    // Get document IDs for batch fetching
    const documentIds = basicDocs?.map(doc => doc.id) || [];

    const totalCount = typeof count === 'number' ? count : null;
    const fetchedCount = documentIds.length;
    const nextOffset = offset + fetchedCount;
    const hasMore = typeof totalCount === 'number'
      ? nextOffset < totalCount
      : fetchedCount === limit;

    if (documentIds.length === 0) {
      return res.json({
        documents: [],
        pagination: {
          limit,
          offset,
          nextOffset,
          total: totalCount,
          hasMore,
        },
      });
    }

    // Use batch fetch to get documents with comments, activity logs, versions, collaborators, and signatures
    const documentsWithMetadata = await batchFetchDocuments(userClient, userId, {
      documentIds,
      includeComments: true,
      includeActivityLogs: true,
      includeMetadata: false,
      includeVersions: true,
      includeCollaborators: true,
      includeSignatures: true
    });

    // Merge basic document data with metadata
    const enrichedDocuments = basicDocs?.map(basicDoc => {
      const enrichedDoc = documentsWithMetadata.find(doc => doc.id === basicDoc.id);
      return {
        ...basicDoc,
        comments: enrichedDoc?.comments || [],
        activityLogs: enrichedDoc?.activityLogs || [],
        versions: enrichedDoc?.versions || [],
        collaborators: enrichedDoc?.collaborators || [],
        signatures: enrichedDoc?.signatures || []
      };
    }) || [];

    res.json({
      documents: enrichedDocuments,
      pagination: {
        limit,
        offset,
        nextOffset,
        total: totalCount,
        hasMore,
      },
    });
  } catch (error) {
    console.error('Error fetching documents:', error);

    // Provide more specific error messages based on error type
    if (error instanceof ValidationError) {
      return res.status(400).json({ error: error.message });
    } else if (error instanceof DatabaseError) {
      return res.status(500).json({ error: `Database error: ${error.message}` });
    } else if (error instanceof CacheError) {
      // Cache errors shouldn't fail the request, but log them
      console.warn('Cache error during document fetch:', error.message);
      return res.status(500).json({ error: 'Failed to fetch documents due to cache issues' });
    } else {
      // Generic error handling
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return res.status(500).json({ error: `Failed to fetch documents: ${errorMessage}` });
    }
  }
});

// Batch fetch documents with optional comments and metadata
router.post('/batch', requireAuth, async (req: AuthRequest, res) => {
  const parse = batchRequestSchema.safeParse(req.body);
  if (!parse.success) {
    return res.status(400).json({ error: parse.error.message });
  }

  const userId = req.user?.id;
  if (!userId) {
    return res.status(401).json({ error: 'User not authenticated' });
  }

  const { documentIds, includeComments, includeMetadata, includeActivityLogs, includeVersions, includeCollaborators, includeSignatures } = parse.data;

  try {
    const userClient = getUserClient(getAccessToken(req));
    const documents = await batchFetchDocuments(userClient, userId, {
      documentIds,
      includeComments,
      includeActivityLogs,
      includeMetadata,
      includeVersions,
      includeCollaborators,
      includeSignatures
    });

    res.json({ documents });
  } catch (error) {
    // Handle different error types with appropriate status codes
    if (error instanceof ValidationError) {
      return res.status(400).json({
        error: error.message,
        type: 'validation_error'
      });
    }

    if (error instanceof DatabaseError) {
      return res.status(503).json({
        error: 'Database service temporarily unavailable',
        type: 'database_error'
      });
    }

    if (error instanceof CacheError) {
      // Cache errors shouldn't affect the response
    }

    // Generic error handling
    return res.status(500).json({
      error: 'An unexpected error occurred while fetching documents',
      type: 'internal_error'
    });
  }
});

// Create a document
router.post('/', requireAuth, async (req: AuthRequest, res) => {
  const parse = docInsertSchema.safeParse(req.body);
  if (!parse.success) {return res.status(400).json({ error: parse.error.message });}
  const userId = req.user?.id;
  if (!userId) {return res.status(401).json({ error: 'User not authenticated' });}
  const userClient = getUserClient(getAccessToken(req));
  // Enforce monthly quota for free plan users
  try {
    const { data: prof } = await userClient.from('profiles').select('plan_name').eq('id', userId).single();
    const planName = (prof?.plan_name as string) || 'Registered User';
    if (planName === 'Registered User') {
      const { count } = await userClient
        .from('usage_events')
        .select('id', { count: 'exact', head: true })
        .gte('created_at', new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString());
      if ((count ?? 0) >= 5) {
        return res.status(403).json({ error: 'Monthly quota reached for Registered User plan' });
      }
    }
  } catch {
    return res.status(500).json({ error: 'Failed to check quota' });
  }
  const { name, content, folderId, status, clientId, value, tags } = parse.data;
  const { data, error } = await userClient
    .from('documents')
    .insert({
      user_id: userId,
      name,
      content,
      folder_id: folderId ?? null,
      status,
      client_id: typeof clientId === 'undefined' ? null : clientId,
      value,
      tags,
    })
    .select('id, name, content, status, folder_id, created_at, updated_at')
    .single();
  if (error) {return res.status(500).json({ error: getSupabaseErrorMessage(error) });}
  // Log usage event for generation (best-effort; ignore failures)
  try {
    await userClient
      .from('usage_events')
      .insert({ user_id: userId, event: 'generation' })
      .select('id, user_id, event, created_at')
      .single();
  } catch {
    // ignore logging errors
  }

  // Create activity log for document creation
  const userEmail = getUserEmailFromRequest(req);
  await createActivityLog(
    userClient,
    ActivityLogHelpers.documentCreated(userEmail, data.id, data.name)
  );

  // Invalidate user documents cache
  await deleteFromCache(cacheKeys.userDocuments(userId));

  res.status(201).json({ document: data });
});

// Update a document
router.put('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parse = docUpdateSchema.safeParse(req.body);
  if (!parse.success) {return res.status(400).json({ error: parse.error.message });}
  const userClient = getUserClient(getAccessToken(req));

  const { data: existingDoc, error: existingError } = await userClient
    .from('documents')
    .select('id, name, content, status, value, client_id, user_id')
    .eq('id', id)
    .single();

  if (existingError || !existingDoc) {
    return res.status(404).json({ error: 'Document not found' });
  }

  // If content is being updated, create a version first
  if ('content' in parse.data) {
    const { error: versionError } = await userClient
      .from('document_versions')
      .insert({
        document_id: id,
        content: existingDoc.content,
        version_type: 'auto'
      });

    if (versionError) {
      return res.status(500).json({ error: 'Failed to create document version' });
    }
  }

  const previousStatus = existingDoc.status;

  const patch: Partial<{
    name: string;
    content: string;
    folder_id: string | null;
    status: string;
    client_id: string | null;
    value: number;
    tags: string[];
  }> = { ...parse.data };
  // Convert camelCase to snake_case for database
  if ('folderId' in parse.data) {
    patch.folder_id = parse.data.folderId;
  }
  if ('clientId' in parse.data) {
    patch.client_id = parse.data.clientId;
  }
  const { data, error } = await userClient
    .from('documents')
    .update({ ...patch, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select('id, name, content, status, folder_id, created_at, updated_at, client_id, value, user_id')
    .single();
  if (error) {return res.status(500).json({ error: getSupabaseErrorMessage(error) });}

  // Create activity log for document edit
  const userEmail = getUserEmailFromRequest(req);
  await createActivityLog(
    userClient,
    ActivityLogHelpers.documentEdited(userEmail, data.id, data.name)
  );

  if (patch.status && patch.status !== previousStatus) {
    const targetUserId = data.user_id ?? existingDoc.user_id ?? req.user?.id;
    if (targetUserId) {
      eventBus.emit('documentStatusChanged', {
        documentId: data.id,
        documentName: data.name,
        newStatus: data.status,
        previousStatus,
        documentValue: data.value ?? 0,
        clientId: data.client_id ?? '',
        audienceUserId: targetUserId,
      }); // forwarded to SSE clients
    }
  }

  // Invalidate user documents cache
  const userId = req.user?.id;
  if (userId) {
    await deleteFromCache(cacheKeys.userDocuments(userId));
  }

  res.json({ document: data });
});

// Delete a document
router.delete('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const userClient = getUserClient(getAccessToken(req));
  const { error } = await userClient
    .from('documents')
    .delete()
    .eq('id', id);
  if (error) {return res.status(500).json({ error: getSupabaseErrorMessage(error) });}

  // Invalidate user documents cache
  const userId = req.user?.id;
  if (userId) {
    await deleteFromCache(cacheKeys.userDocuments(userId));
  }

  res.status(204).end();
});

export default router;
